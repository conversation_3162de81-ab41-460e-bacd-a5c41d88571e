
services:
  opensearch:
    image: opensearchproject/opensearch:2.13.0
    container_name: opensearch
    environment:
      - discovery.type=single-node
      - plugins.security.disabled=true
      - bootstrap.memory_lock=true
      - OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m
      - OPENSEARCH_INITIAL_ADMIN_PASSWORD=T4r@n$#2wQ!
      - plugins.security.ssl_only=false
    volumes:
      - ./opensearch.yml:/usr/share/opensearch/config/opensearch.yml
    ulimits:
      memlock:
        soft: -1
        hard: -1
    ports:
      - "9200:9200"
    networks:
      - opensearch-net

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5001:5000"
    environment:
      - PORT=5000
      - DEV_PORT=5001
      - JWT_SECRET=your_jwt_secret_key
      - JWT_EXPIRE=30d
      - JWT_COOKIE_EXPIRE=30
    depends_on:
      - opensearch
      - postgres
    networks:
      - opensearch-net
    command: ["npm", "start"]
  postgres:
    image: postgres:15
    container_name: postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=InFiniteSQ
      - POSTGRES_DB=INSQ
    ports:
      - "5432:5432"
    networks:
      - opensearch-net
    volumes:
      - pgdata:/var/lib/postgresql/data
      - ./pg_hba.conf:/etc/postgresql/pg_hba.conf
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      
    command: >
      bash -c "cp /etc/postgresql/pg_hba.conf /tmp/pg_hba.conf &&
               chmod 600 /tmp/pg_hba.conf &&
               chown postgres:postgres /tmp/pg_hba.conf &&
               docker-entrypoint.sh postgres -c hba_file=/tmp/pg_hba.conf"



networks:
  opensearch-net:
    driver: bridge

volumes:
  pgdata:
