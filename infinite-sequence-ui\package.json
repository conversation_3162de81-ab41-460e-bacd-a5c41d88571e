{"name": "infinite-sequence-ui", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "bootstrap": "^5.3.5", "cors": "^2.8.5", "date-fns": "^4.1.0", "infinite-sequence-ui": "file:", "lodash": "^4.17.21", "lucide-react": "^0.539.0", "react": "^19.0.0", "react-bootstrap": "^2.10.10", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.1", "reactflow": "^11.11.4"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "vite": "^6.3.5"}}