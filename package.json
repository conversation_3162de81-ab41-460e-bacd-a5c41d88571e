{"name": "Infinite sequence", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "set NODE_ENV=development && nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "create-indices": "node scripts/createIndices.js", "db": "node scripts/setupDatabase.js", "db:dev": "NODE_ENV=development node scripts/setupDatabase.js"}, "keywords": [], "author": "<EMAIL>", "license": "ISC", "description": "Infinite sequence ", "dependencies": {"@opensearch-project/opensearch": "^3.5.1", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.10.1", "pg": "^8.16.0", "razorpay": "^2.9.6", "uuid": "^11.1.0"}, "devDependencies": {"nodemon": "^3.1.10"}}