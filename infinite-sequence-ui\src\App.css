@import "tailwindcss";


body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


#root {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.secondary_title{
  color: #815f55, 
}


main {
  flex: 1;
}

/* Override Bootstrap card styles for React Flow */
.react-flow .card {
  padding: 0;
}

/* Flow editor styles */
.flow-editor-container {
  height: 80vh;
  width: 100%;
}

@keyframes borderAnimation {
  0% { border-color: red; }
  50% { border-color: blue; }
  100% { border-color: red; }
}

.selected-node {
  border: 2px solid red;
  animation: borderAnimation 1s infinite;
}
.menu-over:hover{
  background-color: #9fccf1;
}
.sideMenu-item {
  background-color: white;
  height: 2rem;
  border: none;

}

.sideMenu-item.selected{
  background-color: #f7f7f7;
}
    
.selected-side-item {
  background-color:  #9fccf1;
}

.sidebar-line {
  margin: .5rem;
}
.w-30 {
  width: 30%;
}
.w-70 {
  width: 70%;
}

.invalid-container{
  position:absolute
}

.mb-spe-4 {
margin-bottom: 26px;
}
.roles-list {
  list-style-type: none;
  padding: 0;
}
.roles-list-para {
  margin:0
}
.share-sequence-container {
  max-width: 600px;
  margin: auto;
  padding: 1rem;
  background: #f9f9f9;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 1rem;
}

.toggle-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.team-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button[type='button']:first-child {
  background-color: #ccc;
}

button[type='button']:last-child {
  background-color: #007bff;
  color: white;
}
.dashboard-container {
  display: grid;
  grid-template-columns: 250px 1fr 250px;
  
  
}

.sidebar, .quick-actions {
  background-color: #EFEFF0;
  border-right: 1px solid #E5E7EB; 
}

.main-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dashboard_main{
  background-color: #F8FAFC;
}
.sidebar-button {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.6rem 1rem;
  background-color: transparent;
  color: #334155; /* slate-700 */
  font-size: 0.95rem;
  font-weight: 500;
  border: none;
  border-radius: 0.5rem; /* 8px */
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease;
}
.sidebar-button:hover {
  background-color: #334155; /* slate-100 */
  color: #F8FAFC; /* slate-900 */
}
.sidebar-button.active {
  background-color: #334155; /* slate-200 */
  color: #F8FAFC; /* slate-900 */
  font-weight: 600;
}
.dashboard-main {
  background-color: #F2F4F6;
}

.dashboard-header {
  background-color: #F2F4F6;
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: x-large;
    margin: 0;
    font-weight: 700;
  /* subtle divider */
}

.dashboard-tagline{
  font-size: 0.875rem;       /* 14px */
  color: #6B7280;           /* slate-500 */
  margin-top: 0.25rem;      /* 4px spacing below h1 */
  line-height: 1.5;         /* better readability */
}